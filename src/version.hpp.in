/*!
 * @file version.hpp
 *
 * @brief Version information for the entire project.
 */

#pragma once

#include <string>

static const int32_t RACON_VERSION_MAJOR = @RACON_VERSION_MAJOR@;
static const int32_t RACON_VERSION_MINOR = @RACON_VERSION_MINOR@;
static const int32_t RACON_VERSION_PATCH = @RACON_VERSION_PATCH@;
static const std::string RACON_VERSION_COMMIT("@RACON_VERSION_COMMIT@");

static const std::string RACON_VERSION_STRING =
    std::to_string(RACON_VERSION_MAJOR) + "." +
    std::to_string(RACON_VERSION_MINOR) + "." +
    std::to_string(RACON_VERSION_PATCH) + "-" +
    RACON_VERSION_COMMIT;

static const std::string COMPILE_DATE = (std::string(__DATE__) + std::string(" at ") + std::string(__TIME__));
